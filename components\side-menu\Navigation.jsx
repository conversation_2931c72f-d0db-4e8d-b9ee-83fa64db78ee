'use client';
import { motion } from 'framer-motion';

// Portfolio sections data
const portfolioSections = [
    {
        title: "Home",
        section: 1,
        description: "Welcome & Introduction"
    },
    {
        title: "About",
        section: 2,
        description: "My Story & Background"
    },
    {
        title: "Skills",
        section: 3,
        description: "Technical Expertise"
    },
    {
        title: "Projects",
        section: 4,
        description: "Featured Work"
    },
    {
        title: "Experience",
        section: 5,
        description: "Professional Journey"
    },
    {
        title: "Contact",
        section: 6,
        description: "Get In Touch"
    },
    {
        title: "Demo",
        section: 7,
        description: "Interactive Experience"
    }
];

const socialLinks = [
    {
        title: "GitHub",
        href: "https://github.com/boldbat"
    },
    {
        title: "LinkedIn",
        href: "https://linkedin.com/in/boldbat"
    },
    {
        title: "Twitter",
        href: "https://twitter.com/boldbat"
    },
    {
        title: "Email",
        href: "mailto:<EMAIL>"
    }
];

// Animation variants
const perspective = {
    initial: {
        opacity: 0,
        rotateX: 90,
        translateY: 80,
        translateX: -20,
    },
    enter: (i) => ({
        opacity: 1,
        rotateX: 0,
        translateY: 0,
        translateX: 0,
        transition: {
            duration: 0.65,
            delay: 1.0 + (i * 0.15), // Start after background completes (0.75s) + 0.25s buffer
            ease: [.215,.61,.355,1],
            opacity: { duration: 0.35}
        }
    }),
    exit: {
        opacity: 0,
        transition: { duration: 0.5, type: "linear", ease: [0.76, 0, 0.24, 1]}
    }
};

const slideIn = {
    initial: {
        opacity: 0,
        y: 20
    },
    enter: (i) => ({
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5,
            delay: 2.2 + (i * 0.08), // Start after main navigation items have appeared
            ease: [.215,.61,.355,1]
        }
    }),
    exit: {
        opacity: 0,
        transition: { duration: 0.5, type: "tween", ease: "easeInOut"}
    }
};

export default function Navigation({ onClose }) {
    const handleSectionClick = (sectionIndex) => {
        // Use fullpage.js API to navigate to section
        if (window.fullpage_api) {
            window.fullpage_api.moveTo(sectionIndex);
        }

        // Close menu after navigation for better UX
        if (onClose) {
            setTimeout(onClose, 300);
        }
    };

    return (
        <div className="flex flex-col justify-between p-6 pt-16 pb-8 md:p-10 md:pt-24 md:pb-12 h-full box-border">
            <div className="flex flex-col gap-3">
                {portfolioSections.map((link, i) => {
                    const { title, section, description } = link;
                    return (
                        <div key={`section_${i}`} className="cursor-pointer group"
                             style={{ perspective: '120px', perspectiveOrigin: 'bottom' }}>
                            <motion.div
                                custom={i}
                                variants={perspective}
                                initial="initial"
                                animate="enter"
                                exit="exit"
                                onClick={() => handleSectionClick(section)}
                                className="block"
                            >
                                <div className="text-black hover:text-emerald-900 transition-colors duration-300">
                                    <h3 className="text-2xl md:text-4xl font-bold mb-1 leading-tight">
                                        {title}
                                    </h3>
                                    <p className="text-xs md:text-sm opacity-70 font-medium">
                                        {description}
                                    </p>
                                </div>
                            </motion.div>
                        </div>
                    );
                })}
            </div>
            
            <motion.div className="flex flex-wrap gap-2">
                {socialLinks.map((link, i) => {
                    const { title, href } = link;
                    return (
                        <motion.a 
                            variants={slideIn}
                            custom={i} 
                            initial="initial"
                            animate="enter"
                            exit="exit"
                            key={`social_${i}`}
                            href={href}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="w-1/2 mt-1 text-black hover:text-emerald-900 transition-colors duration-300 text-sm font-medium"
                        >
                            {title}
                        </motion.a>
                    );
                })}
            </motion.div>
        </div>
    );
}
