import { motion } from "framer-motion"
import { AuroraBackground } from "./ui/aurora-background";
import { cn } from "@/lib/utils";
import Marquee from "@/components/magicui/marquee";
import { stack1, stack2, svgs } from "@/lib/data/stack";
import { Icon } from '@iconify/react'
import GradualSpacing from "@/components/magicui/gradual-spacing";

const ReviewCard = ({
    name,
    parent,
    svg,
    off
}) => {
    return (
        <figure
            className={cn(
                "relative w-48 lg:w-64 backdrop-blur-xl cursor-pointer overflow-hidden rounded-xl border border-gray-100/30 p-3 lg:p-4 mx-1",
            )}
        >
            <div className="blur-lg opacity-20 scale-[4] lg:scale-[6] -z-10 absolute top-0 left-0">
                {!off ? <Icon icon={svg} fontSize={24} className="lg:text-[34px]" /> : <img
                    className="min-h-2 min-w-2 w-6 lg:w-auto"
                    src={svgs[svg]}
                />}
            </div>

            <div className="flex flex-row items-center gap-2">
                {!off ? <Icon icon={svg} fontSize={24} className="lg:text-[34px]" /> : <img
                    className="h-6 lg:h-9"
                    src={svgs[svg]}
                />}

                <div className="flex flex-col">
                    <figcaption className="text-sm lg:text-base font-medium text-white">
                        {name}
                    </figcaption>
                    <p className="text-xs font-medium flex items-center justify-center text-white/70 w-max">
                        {parent}</p>
                </div>
            </div>
        </figure>
    );
};

export default function IconCloudDemo() {
    return (
        <div className="section h-[100svh]  overflow-hidden ">
            <AuroraBackground>
                <div className="relative h-[100svh] w-full flex items-center justify-end overflow-hidden flex-col">
                    {/* Marquee Section */}
                    <div className="relative top-10 lg:top-24 w-full z-10">
                        <Marquee className="[--duration:60s] lg:[--duration:45s] top-1/3">
                            {stack1.map((review, index) => (
                                <ReviewCard parent={review.parent} key={index} {...review} />
                            ))}
                        </Marquee>
                        <Marquee reverse className="[--duration:60s] lg:[--duration:45s] top-1/3">
                            {stack2.map((review, index) => (
                                <ReviewCard parent={review.parent} key={index} {...review} />
                            ))}
                        </Marquee>
                    </div>

                    {/* Character Images */}
                    <div className="relative flex-1 flex items-end justify-center w-full">
                        {/* Desktop Image */}
                        <motion.img
                            initial={{ translateY: 400 }}
                            whileInView={{ translateY: 50 }}
                            transition={{ duration: 1, type: "spring", stiffness: 20 }}
                            className="hidden lg:block h-auto max-h-[40vh] z-20"
                            src="/9.png"
                            alt="Character illustration" />

                        {/* Mobile Image */}
                        <motion.div
                            initial={{ translateY: 400 }}
                            whileInView={{ translateY: 50 }}
                            transition={{ duration: 1, type: "spring", stiffness: 20 }}
                            className="flex lg:hidden z-20 w-full items-center justify-center"
                            style={{
                                backgroundColor: 'rgba(0,255,0,0.1)', // Temporary debug background
                                minHeight: '150px',
                                border: '1px solid green' // Temporary debug border
                            }}>
                            <img
                                className="h-auto max-h-[30vh] w-auto object-contain"
                                src="/9.png"
                                alt="Character illustration"
                                loading="eager"
                                style={{
                                    display: 'block',
                                    maxWidth: '100%',
                                    height: 'auto',
                                    minHeight: '100px'
                                }}
                                onError={(e) => {
                                    console.error('Mobile image failed to load:', e);
                                    console.error('Image src:', e.target.src);
                                    console.error('Current URL:', window.location.href);
                                    e.target.style.border = '2px solid red';
                                    e.target.style.backgroundColor = 'rgba(255,0,0,0.1)';
                                    e.target.style.minHeight = '100px';
                                    e.target.style.width = '100px';
                                    // Try alternative path
                                    if (!e.target.dataset.retried) {
                                        e.target.dataset.retried = 'true';
                                        e.target.src = './9.png';
                                    }
                                }}
                                onLoad={(e) => {
                                    console.log('Mobile image loaded successfully');
                                    console.log('Image dimensions:', {
                                        width: e.target.naturalWidth,
                                        height: e.target.naturalHeight
                                    });
                                    console.log('Image src:', e.target.src);
                                }} />
                        </motion.div>
                    </div>
                </div>
                <div className="absolute bottom-0 right-0 m-4 lg:m-10 flex items-end justify-end flex-col">
                    <GradualSpacing
                        className="font-display text-center text-2xl sm:text-3xl md:text-4xl lg:text-7xl font-bold tracking-[-0.1em] text-white leading-tight lg:leading-[5rem]"
                        text="Tech Stack That"
                    />
                    <GradualSpacing
                        delay={0.5}
                        className="font-display text-center text-2xl sm:text-3xl md:text-4xl lg:text-7xl font-bold tracking-[-0.1em] text-white leading-tight lg:leading-[5rem]"
                        text="Actually Works"
                    />
                </div>
            </AuroraBackground>
        </div >
    );
}
