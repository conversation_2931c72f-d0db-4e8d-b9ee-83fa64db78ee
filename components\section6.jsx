"use client"
import { motion } from "framer-motion"
import GradualSpacing from "@/components/magicui/gradual-spacing";
import Link from "next/link";


export default function IconCloudDemo() {
    return (
        <div className="section h-full relative">
            <motion.div
                initial={{ opacity: 0, translateX: -50 }}
                whileInView={{ opacity: 1, translateX: 0 }}
                transition={{ duration: 0.6, type: "spring", stiffness: 20 }}
                className="absolute mx-auto p-4 lg:p-10 flex items-center justify-center h-full w-full flex-col space-y-4 z-10">
                <Link href={"mailto:<EMAIL>"}>
                    <div
                        className="w-80 lg:w-72 hover:bg-white hover:text-black ease-in-out duration-200 transition-all bg-black py-4 lg:py-3 text-white flex items-center justify-center text-lg font-bold rounded-lg touch-manipulation"
                    >
                        Email Me
                    </div>
                </Link>
                <Link href={"tel:+97686970213"}>
                    <div
                        className="w-80 lg:w-72 hover:bg-white hover:text-black ease-in-out duration-200 transition-all bg-black py-4 lg:py-3 text-white flex items-center justify-center text-lg font-bold rounded-lg touch-manipulation"
                    >
                        Call Me
                    </div>
                </Link>
                <Link href={"https://linkedin.com/in/boldbat"}>
                    <div
                        className="w-80 lg:w-72 hover:bg-white hover:text-black ease-in-out duration-200 transition-all bg-black py-4 lg:py-3 text-white flex items-center justify-center text-lg font-bold rounded-lg touch-manipulation"
                    >
                        LinkedIn
                    </div>
                </Link>
                <Link href={"https://github.com/boldbat"}>
                    <div
                        className="w-80 lg:w-72 hover:bg-white hover:text-black ease-in-out duration-200 transition-all bg-black py-4 lg:py-3 text-white flex items-center justify-center text-lg font-bold rounded-lg touch-manipulation"
                    >
                        GitHub
                    </div>
                </Link>
            </motion.div>
            <div className="relative h-[100vh] w-full flex items-center justify-center overflow-hidden bg-sky-500 -z-50 ">
                <motion.img
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 1, type: "spring", stiffness: 20 }}
                    src="/6.jpg" className="h-full w-full -z-10 absolute bottom-0 object-cover " alt="Cloud Image" />
                <div className="absolute mx-auto p-4 lg:p-10 flex items-end justify-start h-full w-full flex-col">
                    <div className='w-max flex items-start justify-start flex-col z-10 translate-y-10'>
                        <GradualSpacing
                            className="font-display text-center text-2xl sm:text-3xl md:text-4xl lg:text-7xl font-bold tracking-[-0.1em] text-white opacity-30 blur leading-tight lg:leading-[5rem]"
                            text="Let's Build Something Cool"
                        />
                    </div>
                </div>
                <div className="absolute mx-auto p-4 lg:p-10 flex items-end justify-start h-full w-full flex-col">
                    <div className='w-max flex items-start justify-start flex-col z-10 translate-y-10'>
                        <GradualSpacing
                            className="font-display text-center text-2xl sm:text-3xl md:text-4xl lg:text-7xl font-bold tracking-[-0.1em] text-white leading-tight lg:leading-[5rem]"
                            text="Let's Build Something Cool"
                        />
                    </div>
                </div>

            </div>
        </div >
    );
}
