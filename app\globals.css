@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  overflow: hidden;
  font-family: Inter, sans-serif;
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  body {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }
}

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Smooth scrolling for mobile */
@supports (-webkit-overflow-scrolling: touch) {
  .overflow-auto {
    -webkit-overflow-scrolling: touch;
  }
}

/* Hide scrollbar but keep functionality */
.no-visible-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.no-visible-scrollbar::-webkit-scrollbar {
  display: none;
}



.fp-watermark {
  display: none !important;
}

@layer base {
  :root {
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
  }
}

::-webkit-scrollbar {
  margin: 10px;
  @apply bg-transparent;
  width: 8px;
  height: 8px !important;
  /* adjust as needed */
}

::-webkit-scrollbar-track {
  background-color: white;
  /* set to desired background color for the scrollbar holder */
}

::-webkit-scrollbar-thumb {
  background: hsl(0, 0%, 0%);
  /* set to desired gradient for the scrollbar thumb */
  border-radius: 6px;
  /* adjust as needed */
}

