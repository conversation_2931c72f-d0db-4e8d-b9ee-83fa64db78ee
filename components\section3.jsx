"use client"
import { motion } from "framer-motion"
import { Tabs } from "../components/ui/tabs";
import Link from "next/link";
import GradualSpacing from "@/components/magicui/gradual-spacing";

export default function IconCloudDemo() {
    return (
        <div className="section h-full">
            <div className="relative h-[100svh] w-full flex items-center justify-center overflow-hidden bg-purple-500">
                <div className="absolute top-0 h-full w-full z-10 hidden lg:flex items-center justify-center  ">
                    <motion.img
                        initial={{ translateX: 200 }}
                        whileInView={{ translateX: 0 }}
                        transition={{ duration: 1, type: "spring", stiffness: 20 }}
                        src="5.png" className="z-50 absolute h-40 right-0" />
                    <motion.img
                        initial={{ translateX: -200 }}
                        whileInView={{ translateX: -40 }}
                        transition={{ duration: 1, type: "spring", stiffness: 20 }}
                        src="4.png" className="z-50 left-0 absolute h-40 -translate-x-6" />
                </div>
                <motion.div
                    whileInView={{ translateY: 0 }}
                    initial={{ translateY: -130 }}
                    transition={{ duration: 1, type: "spring", stiffness: 20 }}
                    className="lg:hidden flex absolute  top-0" >
                    <img
                        src="5.png" className="z-50  h-40 -rotate-45 translate-x-48" />
                </motion.div>
                <motion.div
                    whileInView={{ translateY: 20 }}
                    initial={{ translateY: 150 }}
                    transition={{ duration: 1, type: "spring", stiffness: 20 }}
                    className="lg:hidden flex absolute  bottom-0">
                    <img
                        src="4.png" className="z-50  h-40 -rotate-45 -translate-x-48" />
                </motion.div>
                <div className="flex w-max items-end flex-col justify-end absolute z-50 top-0 left-0 m-4 lg:m-10">
                    <GradualSpacing
                        className="font-display text-center text-2xl sm:text-3xl md:text-4xl lg:text-7xl font-bold tracking-[-0.1em] text-black leading-tight lg:leading-[5rem]"
                        text="BOLDBAT's Side Projects"
                    />
                </div>
                <div className="w-11/12 lg:w-3/5 flex lg:items-start items-center lg:justify-center lg:-translate-y-12 h-full z-50 px-4 lg:px-0">
                    <TabsDemo />
                </div>
            </div>
        </div>
    );
}



export function TabsDemo() {

    return (
        <div className="h-[20rem] md:h-[40rem] [perspective:1000px] relative flex flex-col max-w-5xl mx-auto w-full items-start justify-start lg:my-40 lg:translate-y-8">
            <Tabs tabs={stack.map((v) => {
                return {
                    title: v.name,
                    value: v.name,
                    content: <div className="w-full overflow-hidden relative rounded-xl text-white text-lg sm:text-xl md:text-4xl font-bold h-52 lg:h-full p-4 lg:p-10 lg:bg-purple-400 lg:shadow-purple-400 lg:shadow-[0px_0px_25px_5px]">
                        <p className="lg:block hidden">{v.name}</p>
                        <DummyContent path={v.url} url={v.images[0]} />
                    </div>
                }
            })} />
        </div>
    );
}

const DummyContent = ({ url, path }) => {
    return (
        <Link href={`${path}`}>
            <img
                src={url}
                alt="dummy image"
                className="object-cover object-left-top  absolute inset-x-0  rounded-xl mx-auto  lg:w-[90%] lg:mt-5"
            />
        </Link>
    );
};


const stack = [{
    "name": "LookLuxe AI Outfit Assistant",
    "description": "👗 AI-powered fashion assistant that helps users create perfect outfits. Uses advanced machine learning to analyze style preferences, body type, and occasion to suggest personalized clothing combinations.",
    "tags": "ai fashion machine-learning computer-vision style-recommendation",
    "url": "#",
    "images": [
        "/pics_of_projects/lookluxe.avif",
        "/pics_of_projects/lookluxe.avif"
    ],
    "md": 1
},
{
    "name": "Astro AI - Chatbot Assistant",
    "description": "🤖 Intelligent conversational AI assistant powered by advanced natural language processing. Provides personalized assistance, answers questions, and helps with various tasks through intuitive chat interface.",
    "tags": "ai chatbot nlp conversational-ai assistant machine-learning",
    "url": "#",
    "images": [
        "/pics_of_projects/astro-ai.avif",
        "/pics_of_projects/astro-ai.avif"
    ],
    "md": 2
},
{
    "name": "Neo AI - Logo Generator",
    "description": "🎨 AI-driven logo creation platform that generates unique, professional logos based on user preferences. Combines creative design principles with machine learning to produce brand-ready graphics.",
    "tags": "ai logo-design generative-ai graphics design branding",
    "url": "#",
    "images": [
        "/pics_of_projects/neo-ai.avif",
        "/pics_of_projects/neo-ai.avif"
    ],
    "md": 3
}
]