import "./globals.css";
import { Analytics } from "@vercel/analytics/react"

export const metadata = {
  title: "BOLDBAT.Khuukhenduu - Full-Stack Developer",
  description: "Discover the exceptional full-stack development skills of BOLDBAT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CEO of Oyu Intelligence LLC. Specializing in AI/ML, Vue, React, Node.js, Go, MongoDB, and React Native. Expert in web development and AI projects. Contact BOLDBAT.Khuukhenduu for your next project.",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" />
        <link rel="icon" href="/nono.jpg" sizes="any" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
        <meta name="theme-color" content="#000000" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      </head>
      <body className="h-full w-full ">
        <Analytics />
        {children}
      </body>
    </html>
  );
}
