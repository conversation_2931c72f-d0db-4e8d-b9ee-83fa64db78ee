"use client";
import { motion } from "framer-motion"
import ReactFullpage from "@fullpage/react-fullpage";
import RetroGrid from "@/components/magicui/retro-grid";
import DotPattern from "@/components/magicui/dot-pattern";
import { cn } from "@/lib/utils";
import WordPullUp from "@/components/magicui/word-pull-up";
import { VelocityScroll } from "@/components/magicui/scroll-based-velocity";
import Section3 from "@/components/section3";
import Section4 from "@/components/section4";
import Section5 from "@/components/section5";
import Section6 from "@/components/section6";
import { useEffect, useState } from "react";

export default function Home() {

  const [api, setApi] = useState()

  return (
    <div className="w-full h-[100svh] relative">
      <div className="absolute z-50 m-3 bottom-4 lg:top-1/2 lg:bottom-1/2 lg:right-0 lg:m-6">
        <div className="gap-2 lg:gap-1 flex flex-row lg:flex-col">
          {[...Array(6)].map((_, index) => (
            <button
              key={`nav-dot-${index}`}
              onClick={() => api && api.moveTo(index + 1)}
              className={`${
                api && index === api.getActiveSection().index - 1
                  ? "bg-white scale-110"
                  : "bg-white/40 hover:bg-white/60"
              } h-6 w-6 lg:h-4 lg:w-4 rounded-full border border-black/20 transition-all duration-200 ease-in-out touch-manipulation`}
              aria-label={`Go to section ${index + 1}`}
            />
          ))}
        </div>
      </div>
      <ReactFullpage
        scrollingSpeed={600}
        touchSensitivity={15}
        normalScrollElementTouchThreshold={5}
        bigSectionsDestination="top"
        render={({ state, fullpageApi }) => {
          setApi(fullpageApi)
          return (
            <>
              <ReactFullpage.Wrapper>
                <div className="section h-full">
                  <div className="relative h-[100svh] w-full bg-orange-600 flex overflow-hidden">
                    <RetroGrid />
                    <motion.div
                      initial={{ translateX: -600 }}
                      whileInView={{ translateX: 0 }}
                      transition={{ delay: 0.2, duration: 1, type: "spring", stiffness: 20 }}
                      className="relative h-[100svh] w-full  hidden lg:flex ">
                      <img className="w-full h-full object-cover" src="2.jpg" />
                      <img src="/1.png" className="h-full object-cover -translate-x-1/2" />
                    </motion.div>
                    <div className="h-full w-full flex items-start text-black justify-center flex-col space-y-6 relative px-4 lg:px-0">
                      <WordPullUp
                        className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-[-0.02em] leading-tight md:leading-[5rem] p-4 lg:p-10 text-center lg:text-left"
                        words="BOLDBAT.Khuukhenduu, CEO & AI Architect"
                      />
                      <div className="flex items-center justify-center w-full bottom-8 absolute">
                        <button
                          onClick={() => {
                            fullpageApi.moveSectionDown()
                          }}
                          className="border-2 border-black h-12 w-8 lg:h-12 lg:w-7 rounded-full z-50 animate-pulse hover:bg-black/10 transition-colors duration-200 touch-manipulation"
                          aria-label="Scroll to next section"
                        >
                          <div className="h-full w-full relative p-1.5">
                            <div className="absolute h-3 w-3 bg-black rounded-full animate-bounce bottom-1.5 left-1/2 transform -translate-x-1/2" />
                          </div>
                        </button>
                      </div>
                    </div>

                  </div>
                </div>
                <div className="section h-full">
                  <div id="second" className="relative h-[100svh] w-full bg-indigo-500 flex items-center justify-center overflow-hidden">
                    <DotPattern
                      className={cn(
                        "[mask-image:radial-gradient(300px_circle_at_center,black,transparent)] lg:[mask-image:radial-gradient(500px_circle_at_center,black,transparent)]",
                      )}
                    />

                    <motion.div
                      initial={{ translateY: 600 }}
                      whileInView={{ translateY: 200 }}
                      transition={{ delay: 0, duration: 1, type: "spring", stiffness: 20 }}
                      className="w-64 lg:w-96 h-full translate-y-1/3 rounded-full border-[30px] lg:border-[55px] absolute flex border-indigo-700" />
                    <motion.div
                      initial={{ translateY: 600 }}
                      whileInView={{ translateY: 200 }}
                      transition={{ delay: 0, duration: 1, type: "spring", stiffness: 20 }}
                      className="w-64 lg:w-96 h-full translate-y-1/3 rounded-full border-[30px] lg:border-[55px] absolute flex border-indigo-700 blur-2xl" />
                    <motion.img
                      initial={{ translateY: 300 }}
                      whileInView={{ translateY: 100 }}
                      transition={{ delay: 0, duration: 1, type: "spring", stiffness: 20 }}
                      src="3.png" className="absolute h-[70svh] lg:h-[87svh] -bottom-0 object-cover lg:z-10" />
                    <div className="w-full h-full absolute flex items-center px-4">
                      <VelocityScroll
                        text="Full-Stack Developer & AI Enthusiast from Mongolia • Building Cool Stuff While You're Reading This"
                        default_velocity={1}
                        className="font-display text-center text-2xl sm:text-3xl md:text-4xl lg:text-7xl font-bold tracking-[-0.02em] text-black drop-shadow-sm dark:text-white leading-tight lg:leading-[5rem]"
                      />
                    </div>
                  </div>

                </div>
                <Section3 />
                <Section4 />
                <Section5 />
                <Section6 />
              </ReactFullpage.Wrapper>
            </>
          );
        }}
      />
    </div>
  )
}